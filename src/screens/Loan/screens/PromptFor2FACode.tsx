import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import React, {memo, useState} from 'react';
import {StyleSheet, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';

import MButton from '@/components/MButton';
import GlobalStyles from '@/constants/GlobalStyles';
import {useAppDispatch, useLoan} from '@/hooks/redux';
import {AuthenticationInstance} from '@/services/BackendServices';
import {setAccessToken, setIsRegistered} from '@/storage/actions/loanActions';
import {BodyMB, Footer, Title} from '@/styles/styled-components';
import {showSuccessToast, showWarningToast} from '@/utils/toast';
import theme from '@styles/theme';
import {VerificationCodeInput} from './settings/two-factor-auth/VerificationCodeInput';

const PromptFor2FACode: React.FC = ({navigation, route}: any) => {
  const {email, tempToken, userId} = route.params;

  const {accessToken} = useLoan();
  const dispatch = useAppDispatch();
  const height = useBottomTabBarHeight();
  const offset = height + (theme.isSmallDevice ? 32 : 46);

  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleVerify2FA = async () => {
    if (code.length !== 6) {
      showWarningToast('Please enter a valid 6-digit code');
      return;
    }

    setLoading(true);

    try {
      const payload: any = {
        userId,
        totp: code,
      };

      if (tempToken) {
        payload.tempToken = tempToken;
      }

      const result = await AuthenticationInstance.post(
        `/user/signin/verify-totp`,
        payload,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        },
      );

      if (result.status === 200) {
        dispatch(setAccessToken(result.data.accessToken));
        dispatch(setIsRegistered(true));

        showSuccessToast('Login successful!');

        try {
          navigation.reset({
            index: 0,
            routes: [{name: 'LoanList'}],
          });
        } catch (loansError) {
          console.error('Error fetching loans:', loansError);
          navigation.reset({
            index: 0,
            routes: [{name: 'LoanConfirmation'}],
          });
        }
      }
    } catch (error: any) {
      console.error('2FA verification error:', error);

      if (error.response?.status === 401) {
        showWarningToast('Invalid verification code. Please try again.');
      } else if (error.response?.status === 400) {
        showWarningToast('Invalid request. Please try logging in again.');
        navigation.goBack();
      } else {
        showWarningToast('Verification failed. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.root}>
      <KeyboardAwareScrollView
        contentContainerStyle={styles.keyboardAware}
        keyboardShouldPersistTaps="handled"
        bottomOffset={82}
      >
        <View style={styles.content}>
          <Title style={{fontSize: 22}}>Enter verification code</Title>

          <BodyMB style={{textAlign: 'center'}}>
            Enter the 6-digit code from your authenticator app for {'\n'}
            <BodyMB style={{fontWeight: '600'}}>{email}</BodyMB>
          </BodyMB>

          <View style={styles.codeContainer}>
            <VerificationCodeInput value={code} onChange={setCode} length={6} />
          </View>
        </View>
      </KeyboardAwareScrollView>

      <KeyboardStickyView offset={{opened: offset}}>
        <Footer>
          <MButton
            text="Verify"
            onPress={handleVerify2FA}
            isLoading={loading}
            disabled={code.length !== 6}
          />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default memo(PromptFor2FACode);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  keyboardAware: {
    flex: 1,
    justifyContent: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.lg,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: GlobalStyles.gray.gray900,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: GlobalStyles.gray.gray700,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: theme.spacing.lg,
  },
  emailText: {
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
  },
  codeContainer: {
    width: '100%',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  codeLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: GlobalStyles.gray.gray900,
    alignSelf: 'flex-start',
  },
});
