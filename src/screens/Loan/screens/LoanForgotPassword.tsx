import {useMutation} from '@tanstack/react-query';
import React, {memo, useCallback, useMemo, useState} from 'react';
import {StyleSheet, View} from 'react-native';

import MButton from '@/components/MButton';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import {BodyM} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {showSuccessToast, showWarningToast} from '@/utils/toast';
import {validateEmailWithMessage} from '../utils/loan-helpers';
import {resetPassword} from '../utils/loan-mutations';

type Props = {
  onClose?: () => void;
};

const LoanForgotPassword = ({onClose}: Props) => {
  const [email, setEmail] = useState<string>('');

  const forgotPasswordMutation = useMutation({
    mutationFn: resetPassword,
  });

  const handleSubmit = useCallback(async () => {
    forgotPasswordMutation.mutate(
      {email},
      {
        onSuccess: () => {
          showSuccessToast(
            `A link to reset your password has been sent to ${email.trim()}`,
          );
        },
        onError: (_) => {
          showWarningToast('Failed to send reset link. Please try again later');
        },
      },
    );
  }, [email, onClose]);

  const validateEmail = useMemo(() => {
    if (!email) return {isValid: false, message: undefined};
    return validateEmailWithMessage(email);
  }, [email]);

  const isFormValid = useMemo(() => {
    return validateEmail.isValid && !forgotPasswordMutation.isPending;
  }, [validateEmail.isValid, forgotPasswordMutation.isPending]);

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <BodyM>
          Enter the email address associated with your account, and we'll email you a link
          to reset your password
        </BodyM>

        <TextInput
          label="Email"
          value={email}
          onChangeText={setEmail}
          placeholder="Enter your email address"
          placeholderTextColor={GlobalStyles.gray.gray700}
        />

        <MButton
          text="Send reset link"
          onPress={handleSubmit}
          disabled={!isFormValid}
          isLoading={forgotPasswordMutation.isPending}
          loadingText="Sending Email..."
        />
      </View>
    </View>
  );
};

export default memo(LoanForgotPassword);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.spacing.xxl,
    marginTop: 52,
  },
});
