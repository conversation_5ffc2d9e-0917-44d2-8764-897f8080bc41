import {useMutation} from '@tanstack/react-query';
import React, {memo, useCallback, useState} from 'react';
import {StyleSheet, View} from 'react-native';

import MButton from '@/components/MButton';
import {TextInput} from '@/components/TextInput';
import GlobalStyles from '@/constants/GlobalStyles';
import theme from '@/styles/theme';
import {showSuccessToast, showWarningToast} from '@/utils/toast';
import {resetPassword} from '../../utils/loan-mutations';

const LoanChangePassword = () => {
  const [email, setEmail] = useState<string>('');

  const forgotPasswordMutation = useMutation({
    mutationFn: resetPassword,
  });

  const handleSubmit = useCallback(async () => {
    forgotPasswordMutation.mutate(
      {email},
      {
        onSuccess: () => {
          showSuccessToast(
            `A link to reset your password has been sent to ${email.trim()}`,
          );
        },
        onError: (_) => {
          showWarningToast('Failed to send reset link. Please try again later');
        },
      },
    );
  }, [email]);

  return (
    <View style={styles.root}>
      <View style={styles.content}>
        <TextInput
          label="Email"
          value={'<EMAIL>'}
          editable={false}
          styles={{
            inputContainer: {backgroundColor: GlobalStyles.gray.gray200},
            input: {color: GlobalStyles.gray.gray800},
          }}
        />

        <View style={styles.buttonContainer}>
          <MButton
            text="Send reset link"
            onPress={handleSubmit}
            isLoading={forgotPasswordMutation.isPending}
            loadingText="Sending Email..."
          />
        </View>
      </View>
    </View>
  );
};

export default memo(LoanChangePassword);

const styles = StyleSheet.create({
  root: {
    flex: 1,
    backgroundColor: 'white',
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.layout.ph.screen,
    gap: theme.layout.gap.screen,
    marginTop: 52,
  },
  buttonContainer: {
    marginTop: theme.spacing.md,
  },
});
