import {useBottomTabBarHeight} from '@react-navigation/bottom-tabs';
import {useMutation} from '@tanstack/react-query';
import React, {useEffect, useState} from 'react';
import {Image, StyleSheet, Text, TouchableOpacity, View} from 'react-native';
import {
  KeyboardAwareScrollView,
  KeyboardStickyView,
} from 'react-native-keyboard-controller';
import SkeletonPlaceholder from 'react-native-skeleton-placeholder';

import Check from '@/assets/icons/check.svg';
import MButton from '@/components/MButton';
import {useLoan} from '@/hooks/redux';
import {goBack} from '@/navigation/utils/navigation';
import {fetchQrCode} from '@/screens/Loan/utils/loan-mutations';
import {BodyM, Caption, Footer} from '@/styles/styled-components';
import theme from '@/styles/theme';
import {VerificationCodeInput} from './VerificationCodeInput';

interface VerifyTotpResponse {
  success: boolean;
  message?: string;
}

const TwoFactorAuthScreen: React.FC = () => {
  const height = useBottomTabBarHeight();
  const {accessToken} = useLoan();

  const [code, setCode] = useState('');
  const [is2FAAlreadyEnabled, setIs2FAAlreadyEnabled] = useState(false);
  const [showSkeleton, setShowSkeleton] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowSkeleton(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, []);

  const onSubmit = () => {
    if (code.length === 6) {
      verifyTotpCode(code);
    }
  };

  const verifyTotp = async (totpCode: string): Promise<VerifyTotpResponse> => {
    try {
      const response = await fetch('http://192.168.10.157/auth/user/otp/verify', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          totp: totpCode,
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('TOTP verification error:', error);
      throw error;
    }
  };

  const {
    mutate: generateQrCode,
    data: qrCodeData,
    isPending: isLoading,
    isError,
    error,
  } = useMutation({
    mutationFn: () => fetchQrCode(accessToken),
    onSuccess: (data) => {
      // QR code loaded successfully
    },
    onError: (error) => {
      // Check if the error indicates 2FA is already enabled
      if (
        error.message.includes('2FA is already enabled') ||
        error.message.includes('"alreadyEnabled":true')
      ) {
        setIs2FAAlreadyEnabled(true);
      }
    },
  });

  const {
    mutate: verifyTotpCode,
    isPending: isVerifying,
    isError: isVerifyError,
    error: verifyError,
    isSuccess: isVerifySuccess,
  } = useMutation<VerifyTotpResponse, Error, string>({
    mutationFn: verifyTotp,
    onSuccess: (data) => {
      // Handle successful verification
    },
    onError: (error) => {
      // Handle verification error
    },
  });

  useEffect(() => {
    if (accessToken) {
      generateQrCode();
    }
  }, [accessToken, generateQrCode]);

  const renderAlreadyEnabledScreen = () => (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={[styles.contentContainer, styles.centeredContent]}
        keyboardShouldPersistTaps="handled"
      >
        <View style={styles.alreadyEnabledContainer}>
          <View style={styles.checkIconContainer}>
            <Check width={80} height={80} />
          </View>

          <Caption style={styles.alreadyEnabledTitle}>
            Two-Factor Authentication is Active
          </Caption>

          <BodyM style={{textAlign: 'center'}}>
            Your account is already protected with two-factor authentication
          </BodyM>
        </View>
      </KeyboardAwareScrollView>

      <KeyboardStickyView
        offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
      >
        <Footer>
          <MButton text="Back to Settings" onPress={goBack} />
        </Footer>
      </KeyboardStickyView>
    </View>
  );

  // Render QR code section
  const renderQrCodeSection = () => {
    // Show skeleton for 2 seconds OR while loading
    if (showSkeleton || isLoading) {
      return (
        <View style={styles.qr}>
          <SkeletonPlaceholder borderRadius={12}>
            <View style={styles.qrSkeleton} />
          </SkeletonPlaceholder>
        </View>
      );
    }

    if (qrCodeData?.data) {
      return (
        <TouchableOpacity>
          <Image style={styles.qr} source={{uri: qrCodeData.data}} />
        </TouchableOpacity>
      );
    }

    if (isError) {
      return (
        <View style={styles.qr}>
          <Text style={styles.errorText}>
            Failed to load QR code
            {error && `\n${error.message}`}
          </Text>
        </View>
      );
    }

    return null;
  };

  const renderVerificationSection = () => {
    if (showSkeleton) {
      return (
        <SkeletonPlaceholder borderRadius={4}>
          <View>
            <View style={styles.verificationLabelSkeleton} />
          </View>
        </SkeletonPlaceholder>
      );
    }

    return (
      <>
        <Text style={styles.verificationLabel}>Verification Code</Text>
        <VerificationCodeInput value={code} onChange={setCode} length={6} />
      </>
    );
  };

  const renderStatusMessages = () => (
    <>
      {isVerifySuccess && (
        <Text style={styles.successText}>
          ✓ Two-factor authentication enabled successfully!
        </Text>
      )}

      {isVerifyError && (
        <Text style={styles.errorText}>
          ✗ Invalid verification code. Please try again.
          {verifyError && `\n${verifyError.message}`}
        </Text>
      )}
    </>
  );

  if (is2FAAlreadyEnabled) {
    return renderAlreadyEnabledScreen();
  }

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
        bottomOffset={140}
      >
        <View>{renderQrCodeSection()}</View>

        {renderVerificationSection()}

        {renderStatusMessages()}
      </KeyboardAwareScrollView>

      <KeyboardStickyView
        offset={{closed: 0, opened: height + (theme.isSmallDevice ? 24 : 40)}}
      >
        <Footer>
          <MButton
            text={isVerifying ? 'Verifying...' : 'Next'}
            disabled={code.length !== 6 || isVerifying}
            onPress={onSubmit}
          />
        </Footer>
      </KeyboardStickyView>
    </View>
  );
};

export default TwoFactorAuthScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 32,
    paddingTop: 42,
  },
  contentContainer: {
    flexGrow: 1,
  },
  centeredContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  descText: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  descSubText: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 24,
  },
  verificationLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
    marginTop: 16,
  },
  qr: {
    width: 200,
    height: 200,
    resizeMode: 'contain',
    alignSelf: 'center',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 80,
  },
  successText: {
    color: '#10B981',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 12,
    fontWeight: '500',
  },
  alreadyEnabledContainer: {
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  checkIconContainer: {
    marginBottom: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  alreadyEnabledTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 16,
  },
  descTextSkeleton: {
    height: 20,
    width: '80%',
    marginBottom: 4,
  },
  descSubTextSkeleton: {
    height: 16,
    width: '100%',
    marginBottom: 24,
  },
  verificationLabelSkeleton: {
    height: 16,
    width: 120,
    marginBottom: 8,
    marginTop: 16,
  },
  qrSkeleton: {
    width: 200,
    height: 200,
    alignSelf: 'center',
  },
});
